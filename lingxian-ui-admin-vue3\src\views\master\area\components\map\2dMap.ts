import { AreaNode } from '@/api/master/area';
import { canvasToImageWithDPR, type ScreenInfo } from '@/store/modules/canvas/drawing';
import FabricCanvas from '@/utils/FabricCanvas';
import {
  FenceFactory,
  AreaFenceManager,
  type AreaPosition,
  type CirclePoints,
  type RectanglePoints,
  type PolygonPoints
} from '@/utils/fence';

// 导出从fence.ts导入的类型，避免重复定义
export type { CirclePoints, RectanglePoints, PolygonPoints, AreaPosition };

/**
 * FabricCanvas扩展工具类
 * 提供围栏绘制相关的方法
 */
export class FabricCanvasExtensions {
  private map: FabricCanvas;
  private fenceFactory: FenceFactory;

  constructor(map: FabricCanvas) {
    this.map = map;
    this.fenceFactory = new FenceFactory(map.canvas);
  }

  /**
   * 添加圆形围栏
   */
  addCircleFence(center: { x: number; y: number }, radius: number = 0) {
    if (!this.map?.canvas) return null;
    return this.fenceFactory.addCircleFence(center, radius);
  }

  /**
   * 添加矩形围栏
   */
  addRectangleFence(position: { x: number; y: number }, width: number = 0, height: number = 0) {
    if (!this.map?.canvas) return null;
    return this.fenceFactory.addRectangleFence(position, width, height);
  }

  /**
   * 添加多边形围栏
   */
  addPolygonFence(points: { x: number; y: number }[]) {
    if (!this.map?.canvas || !points?.length) return null;

    // 直接使用FenceFactory创建多边形围栏
    return this.fenceFactory.createPolygonFence(points);
  }

  /**
   * 设置圆形围栏
   */
  setCircleFence(circle: any, point: { x: number; y: number }, startPoint: { x: number; y: number }) {
    if (!this.map?.canvas || !circle) return;
    this.fenceFactory.setCircleFence(circle, point, startPoint);
  }

  /**
   * 设置矩形围栏
   */
  setRectangleFence(rectangle: any, point: { x: number; y: number }, startPoint: { x: number; y: number }) {
    if (!this.map?.canvas || !rectangle) return;
    this.fenceFactory.setRectangleFence(rectangle, point, startPoint);
  }

  /**
   * 更新半径线
   */
  updateRadiusLine(radiusLine: any, center: { x: number; y: number }, endPoint: { x: number; y: number }) {
    if (!this.map?.canvas || !radiusLine) return;
    this.fenceFactory.updateRadiusLine(radiusLine, center, endPoint);
  }
}

/**
 * 为FabricCanvas实例添加扩展方法
 * @param map FabricCanvas实例
 * @returns 扩展后的实例
 */
export const extendFabricCanvas = (map: FabricCanvas) => {
  const extensions = new FabricCanvasExtensions(map);
  
  // 将扩展方法添加到map实例上
  (map as any).addCircleFence = extensions.addCircleFence.bind(extensions);
  (map as any).addRectangleFence = extensions.addRectangleFence.bind(extensions);
  (map as any).addPolygonFence = extensions.addPolygonFence.bind(extensions);
  (map as any).setCircleFence = extensions.setCircleFence.bind(extensions);
  (map as any).setRectangleFence = extensions.setRectangleFence.bind(extensions);
  (map as any).updateRadiusLine = extensions.updateRadiusLine.bind(extensions);
  
  return map;
};

/**
 * 创建围栏/区域对象
 * @param position 位置数据
 * @param map FabricCanvas实例
 * @param imgInfoSize 图片尺寸信息
 * @param screenInfo 屏幕信息
 * @returns 创建的围栏对象
 */
export const createFence = (
  position: AreaPosition,
  map: FabricCanvas,
) => {
  if (!map?.canvas || !position || position.type === 'none') {
    return null;
  }

  try {
    const fenceManager = new AreaFenceManager(map.canvas);
    return fenceManager.createFence(position);
  } catch (error) {
    console.error('创建围栏失败:', error);
    return null;
  }
};

/**
 * 区域尺寸标注管理器
 */
export const AreaDimensionManager = {
  /**
   * 为区域添加尺寸标注
   * @param areaData 区域数据
   * @param map FabricCanvas实例
   * @param imgInfoSize 图片尺寸信息
   * @param screenInfo 屏幕信息
   * @param isSelected 是否为选中状态
   * @returns 尺寸标注对象数组
   */
  addAreaDimensionMarkers(
    areaData: AreaNode,
    map: FabricCanvas,
    isSelected: boolean = false
  ): any[] {
    if (!map?.canvas) return [];

    try {
      // TODO: 实现区域尺寸标注逻辑
      // 这里应该根据区域类型（圆形、矩形、多边形）创建相应的尺寸标注
      console.log('添加区域尺寸标注:', areaData.id, isSelected);
      return [];
    } catch (error) {
      console.error('添加区域尺寸标注失败:', error);
      return [];
    }
  },

  /**
   * 移除区域的尺寸标注
   * @param areaId 区域ID
   * @param map FabricCanvas实例
   */
  removeAreaDimensionMarkers(areaId: number, map: FabricCanvas) {
    if (!map?.canvas) return;
    
    try {
      const objectsToRemove = map.canvas.getObjects().filter((obj: any) => 
        obj.id && typeof obj.id === 'string' && (
          obj.id === `area-dimension-${areaId}` ||
          obj.id === `area-center-${areaId}` ||
          obj.id === `area-radius-line-${areaId}` ||
          obj.id === `area-width-line-${areaId}` ||
          obj.id === `area-height-line-${areaId}` ||
          obj.id.startsWith(`area-dimension-${areaId}-segment-`)
        )
      );

      objectsToRemove.forEach((obj: any) => {
        map.canvas.remove(obj);
      });

      map.canvas.renderAll();
    } catch (error) {
      console.error('移除区域尺寸标注失败:', error);
    }
  },

  /**
   * 更新区域尺寸标注
   * @param areaData 区域数据
   * @param map FabricCanvas实例
   * @param imgInfoSize 图片尺寸信息
   * @param screenInfo 屏幕信息
   * @param isSelected 是否为选中状态
   * @param forceRecreate 是否强制重新创建
   */
  updateAreaDimensionMarkers(
    areaData: AreaNode,
    map: FabricCanvas,

    isSelected: boolean = false,
    forceRecreate: boolean = false
  ): any[] {
    if (!map?.canvas || !areaData.id) return [];

    try {
      // TODO: 实现区域尺寸标注更新逻辑
      console.log('更新区域尺寸标注:', areaData.id, isSelected, forceRecreate);
      // 先移除旧的标注
      this.removeAreaDimensionMarkers(areaData.id, map);
      // 重新添加标注
      return this.addAreaDimensionMarkers(areaData, map,  isSelected);
    } catch (error) {
      console.error('更新区域尺寸标注失败:', error);
      return [];
    }
  },

  /**
   * 移除所有区域尺寸标注
   * @param map FabricCanvas实例
   */
  removeAllAreaDimensionMarkers(map: FabricCanvas) {
    if (!map?.canvas) return;
    
    try {
      const objectsToRemove = map.canvas.getObjects().filter((obj: any) => 
        obj.id && typeof obj.id === 'string' && (
          obj.id.startsWith('area-dimension-') ||
          obj.id.startsWith('area-center-') ||
          obj.id.startsWith('area-radius-line-') ||
          obj.id.startsWith('area-width-line-') ||
          obj.id.startsWith('area-height-line-')
        )
      );

      objectsToRemove.forEach((obj: any) => {
        map.canvas.remove(obj);
      });

      map.canvas.renderAll();
    } catch (error) {
      console.error('移除所有区域尺寸标注失败:', error);
    }
  }
};

/**
 * 画布状态管理工具
 */
export const CanvasStateManager = {
  /**
   * 清除画布的所有活动对象和选择状态
   * @param map 画布实例
   */
  clearActiveState(map: FabricCanvas) {
    if (!map?.canvas) return;
    
    try {
      map.canvas.discardActiveObject();
      map.canvas.renderAll();
    } catch (error) {
      console.warn('清除活动状态失败:', error);
    }
  },

  /**
   * 更新所有对象的坐标
   * @param map 画布实例
   */
  updateAllObjectCoordinates(map: FabricCanvas) {
    if (!map?.canvas) return;
    
    try {
      map.canvas.getObjects().forEach((obj: any) => {
        if (obj && typeof obj.setCoords === 'function') {
          obj.setCoords();
        }
      });
    } catch (error) {
      console.warn('更新对象坐标失败:', error);
    }
  },

  /**
   * 将对象置于最前并更新
   * @param map 画布实例
   * @param object 要置前的对象
   */
  bringToFrontAndUpdate(map: FabricCanvas, object: any) {
    if (!map?.canvas || !object) return;
    
    try {
      map.canvas.bringObjectToFront(object);
      if (typeof object.setCoords === 'function') {
        object.setCoords();
      }
      map.canvas.renderAll();
    } catch (error) {
      console.warn('置前对象失败:', error);
    }
  },

  /**
   * 重置所有区域为正常状态
   * @param map 画布实例
   * @param areaTree 区域树
   */
  resetAllAreasToNormal(map: FabricCanvas, areaTree: AreaNode[]) {
    if (!map?.canvas || !areaTree?.length) return;
    
    try {
      areaTree.forEach(area => {
        const areaObject = map.findGroupById(area.id as any);
        if (areaObject) {
          setAreaHighlightAndDraggable(areaObject, area, false, false, false, map);
        }
        
        if (area.children?.length) {
          this.resetAllAreasToNormal(map, area.children);
        }
      });
      
      map.canvas.renderAll();
    } catch (error) {
      console.warn('重置区域状态失败:', error);
    }
  }
};

/**
 * 事件处理工具
 */
export const EventHandlerUtils = {
  /**
   * 创建事件节流处理器
   * @param threshold 节流阈值（毫秒）
   * @returns 节流处理函数
   */
  createEventThrottle(threshold: number = 16) {
    let lastTime = 0;
    
    return (callback: () => void) => {
      const now = Date.now();
      if (now - lastTime >= threshold) {
        lastTime = now;
        callback();
      }
    };
  },

  /**
   * 计算拖拽距离
   * @param startPoint 起始点
   * @param currentPoint 当前点
   * @returns 距离
   */
  calculateDragDistance(startPoint: { x: number, y: number }, currentPoint: { x: number, y: number }) {
    if (!startPoint || !currentPoint) return 0;
    
    const dx = currentPoint.x - startPoint.x;
    const dy = currentPoint.y - startPoint.y;
    return Math.sqrt(dx * dx + dy * dy);
  },

  /**
   * 获取动态阈值
   * @param map 画布实例
   * @param baseThreshold 基础阈值
   * @param scaleFactor 缩放因子
   * @returns 动态阈值
   */
  getDynamicThreshold(map: FabricCanvas, baseThreshold: number, scaleFactor: number = 5) {
    if (!map?.canvas) return baseThreshold;
    
    const zoom = map.canvas.getZoom();
    return Math.max(baseThreshold / zoom, scaleFactor);
  }
};

/**
 * 绘制状态管理工具
 */
export const DrawingStateManager = {
  /**
   * 禁用所有区域以进行绘制
   * @param map 画布实例
   */
  disableAllAreasForDrawing(map: FabricCanvas) {
    if (!map?.canvas) return;
    
    try {
      map.canvas.getObjects().forEach((obj: any) => {
        if (obj && obj.id && obj.id !== 'fence') {
          obj.set({
            selectable: false,
            evented: false
          });
        }
      });
      
      map.canvas.selection = false;
      map.canvas.renderAll();
    } catch (error) {
      console.warn('禁用区域失败:', error);
    }
  },

  /**
   * 恢复区域选择状态
   * @param map 画布实例
   * @param currentSelectedAreaId 当前选中的区域ID
   */
  restoreAreasSelectionState(map: FabricCanvas) {
    if (!map?.canvas) return;
    
    try {
      map.canvas.getObjects().forEach((obj: any) => {
        if (obj && obj.id && obj.id !== 'fence') {
          obj.set({
            selectable: true,
            evented: true
          });
        }
      });
      
      map.canvas.selection = true;
      map.canvas.renderAll();
    } catch (error) {
      console.warn('恢复区域状态失败:', error);
    }
  }
};

/**
 * 设置区域颜色样式（单选高亮效果）
 * 用于单击选中区域时的高亮显示，可拖动状态
 * @param data 区域数据或颜色值
 * @param isHighlighted 是否为高亮状态
 * @param map 画布实例
 * @returns 样式对象
 */
export const setAreaColor = (data: AreaNode | any, isHighlighted: boolean = false, map?: FabricCanvas) => {
  let color = data.color;
  if (!/^#([0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/.test(color)) color = '#1e90ff';
  
  const transparentColor = color + (isHighlighted ? '80' : '20');
  const currentZoom = map?.canvas?.getZoom() || 1;
  const highlightWidth = 4 / currentZoom;
  const normalWidth = 0.5 / currentZoom;
  
  return {
    stroke: color,
    fill: transparentColor,
    strokeWidth: isHighlighted ? highlightWidth : normalWidth,
  };
}

/**
 * 设置多选区域颜色样式
 * 用于复选框多选时的高亮显示，与单选区分但使用实线
 * @param data 区域数据或颜色值
 * @param map 画布实例
 * @returns 样式对象
 */
export const setMultiSelectColor = (data: AreaNode | any, map?: FabricCanvas) => {
  let color = data.color;
  if (!/^#([0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/.test(color)) color = '#1e90ff';
  
  const currentZoom = map?.canvas?.getZoom() || 1;
  const borderWidth = 2 / currentZoom;
  
  return {
    stroke: color,
    fill: color + '33',
    strokeWidth: borderWidth,
  };
}

/**
 * 设置区域高亮和拖动状态的通用函数
 * 根据不同的状态组合设置区域的视觉效果和交互能力
 * @param object 画布对象
 * @param areaData 区域数据
 * @param isHighlighted 是否高亮显示
 * @param isDraggable 是否可拖动
 * @param isMultiSelect 是否为多选模式
 * @param map 画布实例
 */
export const setAreaHighlightAndDraggable = (
  object: any, 
  areaData: AreaNode, 
  isHighlighted: boolean, 
  isDraggable: boolean, 
  isMultiSelect: boolean = false,
  map?: FabricCanvas
) => {
  if (!object) return;

  const baseSettings = {
    hasControls: false,
    hasBorders: false,
    lockRotation: true,
    lockScalingX: true,
    lockScalingY: true,
    evented: true,
    objectCaching: false,
    hoverCursor: isDraggable ? 'move' : 'default',
    moveCursor: isDraggable ? 'move' : 'default'
  };

  let settings;
  if (isHighlighted && isDraggable) {
    settings = {
      ...baseSettings,
      selectable: true,
      moveable: true,
      ...setAreaColor(areaData, true, map)
    };
  } else if (isHighlighted && !isDraggable) {
    settings = {
      ...baseSettings,
      selectable: false,
      moveable: false,
      hoverCursor: 'default',
      moveCursor: 'default',
      ...(isMultiSelect ? setMultiSelectColor(areaData, map) : setAreaColor(areaData, true, map))
    };
  } else {
    settings = {
      ...baseSettings,
      selectable: false,
      moveable: false,
      hoverCursor: 'default',
      moveCursor: 'default',
      ...setAreaColor(areaData, false, map)
    };
  }

  object.set(settings);
  
  if (object.setCoords) {
    object.setCoords();
  }
}

/**
 * 获取不同类型区域的中心点
 * @param areaPosition 区域位置数据
 * @returns 中心点坐标
 */
export const getAreaCenter = (areaPosition: AreaPosition): { x: number, y: number } => {
  if (!areaPosition?.points) return { x: 0, y: 0 };

  switch (areaPosition.type) {
    case 'circle':
      const circle = areaPosition.points as CirclePoints;
      return { x: circle.x, y: circle.y };

    case 'rectangle':
      const rect = areaPosition.points as RectanglePoints;
      return {
        x: rect.x + rect.width / 2,
        y: rect.y - rect.height / 2
      };

    case 'polygon':
      const points = areaPosition.points as PolygonPoints;
      if (points.length === 0) return { x: 0, y: 0 };

      let sumX = 0, sumY = 0;
      points.forEach(point => {
        sumX += point.x;
        sumY += point.y;
      });

      return {
        x: sumX / points.length,
        y: sumY / points.length
      };

    default:
      return { x: 0, y: 0 };
  }
};

/**
 * 查找区域节点
 * @param tree 区域树
 * @param targetId 目标ID
 * @returns 找到的区域节点
 */
export const findAreaNodeById = (tree: AreaNode[], targetId: number): AreaNode | null => {
  if (!tree || !Array.isArray(tree) || !targetId) return null;

  const stack = [...tree];
  while (stack.length > 0) {
    const node = stack.pop();
    if (!node) continue;

    if (node.id === targetId) return node;

    if (node.children?.length) {
      stack.push(...node.children);
    }
  }
  return null;
}

/**
 * 更新区域树节点
 * @param tree 区域树
 * @param targetId 目标ID
 * @param areaData 要更新的区域数据
 * @returns 是否更新成功
 */
export const updateAreaTreeNode = (tree: AreaNode[], targetId: number, areaData: AreaNode): boolean => {
  if (!tree || !Array.isArray(tree) || !targetId) return false;

  const stack = [...tree];
  let index = 0;

  while (index < stack.length) {
    const node = stack[index];

    if (node.id === targetId) {
      Object.assign(node, areaData);
      return true;
    }

    if (node.children?.length) {
      stack.push(...node.children);
    }

    index++;
  }

  return false;
}

/**
 * 更新对象位置的函数
 * @param object 画布对象
 * @param areaData 区域数据
 * @param map 画布实例
 * @param imgInfoSize 图片信息
 * @param screenInfo 屏幕信息
 * @param onPositionChanged 位置变更回调
 * @returns 更新后的区域数据
 */
export const updateObjectPosition = (
  object: any, 
  areaData: AreaNode, 
  map: FabricCanvas, 
  imgInfoSize: any, 
  screenInfo: ScreenInfo,
  onPositionChanged?: (data: AreaNode) => void
): AreaNode | null => {
  if (!object || !areaData?.position) return null;

  const updatedAreaData = JSON.parse(JSON.stringify(areaData));

  try {
    switch (object.type) {
      case 'circle':
        const circleResult = canvasToImageWithDPR(
          { x: object.left, y: object.top, radius: object.radius },
          map, imgInfoSize, screenInfo
        );
        
        if (isNaN(circleResult.x) || isNaN(circleResult.y) || isNaN(circleResult.radius)) {
          return null;
        }
        
        updatedAreaData.position = {
          type: 'circle',
          points: {
            x: circleResult.x,
            y: circleResult.y,
            radius: circleResult.radius
          }
        };
        break;
        
      case 'rect':
        const rectResult = canvasToImageWithDPR(
          { 
            x: object.left, 
            y: object.top,
            width: object.width * (object.scaleX || 1),
            height: object.height * (object.scaleY || 1)
          },
          map, imgInfoSize, screenInfo
        );
        
        updatedAreaData.position = {
          type: 'rectangle',
          points: {
            x: rectResult.x,
            y: rectResult.y,
            width: rectResult.width,
            height: rectResult.height
          }
        };
        break;
        
      case 'polygon':
        if (!object.points?.length) return null;
        
        const convertedPoints = object.points.map((point: any) => {
          const converted = canvasToImageWithDPR(point, map, imgInfoSize, screenInfo);
          return { x: converted.x, y: converted.y };
        });
        
        updatedAreaData.position = {
          type: 'polygon',
          points: convertedPoints
        };
        break;
        
      default:
        return null;
    }
    
    const center = getAreaCenter(updatedAreaData.position as AreaPosition);
    if (center && !isNaN(center.x) && !isNaN(center.y)) {
      updatedAreaData.centerPoint = center;
      
      if (onPositionChanged) {
        onPositionChanged(JSON.parse(JSON.stringify(updatedAreaData)));
      }
      
      return updatedAreaData;
    }
    
    return null;
  } catch (error) {
    return null;
  }
}

/**
 * 对象池管理
 */
export const ObjectPool = {
  pool: new Map<string, any[]>(),

  acquire(type: string, createFn: () => any) {
    if (!this.pool.has(type)) {
      this.pool.set(type, []);
    }
    const poolArray = this.pool.get(type)!;
    return poolArray.length > 0 ? poolArray.pop() : createFn();
  },

  release(type: string, obj: any) {
    if (!this.pool.has(type)) {
      this.pool.set(type, []);
    }
    if (obj?.set) {
      obj.set({
        left: 0, top: 0, width: 0, height: 0, radius: 0, points: [],
        selectable: false, hasControls: false
      });
    }
    this.pool.get(type)!.push(obj);
  },

  clear() {
    this.pool.clear();
  }
};

/**
 * 节流渲染函数
 * @param map 画布实例
 * @param lastRenderTime 上次渲染时间
 * @param fps 目标帧率
 * @param pendingRender 是否有待处理的渲染
 * @returns 更新后的状态
 */
export const throttledRender = (
  map: FabricCanvas, 
  lastRenderTime: number, 
  fps: number = 30, // 降低默认FPS以提高性能
  pendingRender: boolean = false
): { lastRenderTime: number, pendingRender: boolean } => {
  if (!map?.canvas) return { lastRenderTime, pendingRender };

  const now = Date.now();
  const frameInterval = 1000 / fps;

  if (now - lastRenderTime >= frameInterval) {
    // 使用requestAnimationFrame优化渲染
    requestAnimationFrame(() => {
      if (map?.canvas) {
        map.canvas.renderAll();
      }
    });
    return { lastRenderTime: now, pendingRender: false };
  } else if (!pendingRender) {
    // 延迟渲染也使用requestAnimationFrame
    setTimeout(() => {
      if (map?.canvas) {
        requestAnimationFrame(() => {
          map.canvas.renderAll();
        });
      }
    }, frameInterval - (now - lastRenderTime));
    return { lastRenderTime, pendingRender: true };
  }
  
  return { lastRenderTime, pendingRender };
}


